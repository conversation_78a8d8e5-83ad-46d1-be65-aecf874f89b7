using System;
using System.Windows.Forms;

namespace SmartHealthcare
{
    /// <summary>
    /// 科室管理页面演示程序
    /// 这个类展示了如何使用科室管理页面
    /// </summary>
    public static class DepartmentManagementDemo
    {
        /// <summary>
        /// 演示科室管理页面的主要功能
        /// 注意：这不是程序的主入口点，需要手动调用
        /// </summary>
        public static void RunDemo()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // 创建演示窗体
            var demoForm = CreateDemoForm();
            Application.Run(demoForm);
        }

        /// <summary>
        /// 简单调用演示程序的方法
        /// 可以在其他地方调用此方法来启动演示
        /// </summary>
        public static void ShowDemo()
        {
            var demoForm = CreateDemoForm();
            demoForm.ShowDialog();
        }

        /// <summary>
        /// 创建演示窗体
        /// </summary>
        /// <returns></returns>
        private static Form CreateDemoForm()
        {
            var form = new Form
            {
                Text = "科室管理页面演示",
                Size = new System.Drawing.Size(600, 400),
                StartPosition = FormStartPosition.CenterScreen
            };

            // 创建标题标签
            var titleLabel = new Label
            {
                Text = "智慧医疗系统 - 科室管理页面演示",
                Font = new System.Drawing.Font("Microsoft YaHei UI", 16, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.FromArgb(54, 78, 111),
                AutoSize = true,
                Location = new System.Drawing.Point(50, 50)
            };

            // 创建说明标签
            var descLabel = new Label
            {
                Text = "点击下面的按钮打开科室管理页面，体验以下功能：\n\n" +
                       "• 左侧：科室和病例的树形显示\n" +
                       "• 右侧上方：科室信息的添加和管理\n" +
                       "• 右侧下方：病例信息的添加和管理\n\n" +
                       "注意：需要后台API服务器运行才能正常使用所有功能",
                Font = new System.Drawing.Font("Microsoft YaHei UI", 10),
                ForeColor = System.Drawing.Color.FromArgb(102, 102, 102),
                Size = new System.Drawing.Size(500, 150),
                Location = new System.Drawing.Point(50, 100)
            };

            // 创建打开科室管理页面的按钮
            var openButton = new Button
            {
                Text = "打开科室管理页面",
                Font = new System.Drawing.Font("Microsoft YaHei UI", 12),
                Size = new System.Drawing.Size(200, 50),
                Location = new System.Drawing.Point(200, 280),
                BackColor = System.Drawing.Color.FromArgb(52, 152, 219),
                ForeColor = System.Drawing.Color.White,
                FlatStyle = FlatStyle.Flat
            };

            openButton.Click += (sender, e) =>
            {
                try
                {
                    var departmentForm = new DepartmentManagementForm();
                    departmentForm.ShowDialog();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开科室管理页面时发生错误:\n{ex.Message}", 
                        "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            // 创建API状态检查按钮
            var checkApiButton = new Button
            {
                Text = "检查API状态",
                Font = new System.Drawing.Font("Microsoft YaHei UI", 10),
                Size = new System.Drawing.Size(120, 35),
                Location = new System.Drawing.Point(420, 280),
                BackColor = System.Drawing.Color.FromArgb(155, 89, 182),
                ForeColor = System.Drawing.Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // 创建JSON测试按钮
            var testJsonButton = new Button
            {
                Text = "测试JSON",
                Font = new System.Drawing.Font("Microsoft YaHei UI", 10),
                Size = new System.Drawing.Size(100, 35),
                Location = new System.Drawing.Point(320, 280),
                BackColor = System.Drawing.Color.FromArgb(46, 204, 113),
                ForeColor = System.Drawing.Color.White,
                FlatStyle = FlatStyle.Flat
            };

            checkApiButton.Click += async (sender, e) =>
            {
                try
                {
                    string url = APIURL.ReadURL + "api/SmartHealthcare/Handle";
                    string response = await HttpClientHelper.ClientAsync("GET", url, false);

                    if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                    {
                        MessageBox.Show("API服务器连接正常", "成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show($"API服务器连接失败:\n{response}", "错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"检查API状态时发生错误:\n{ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            testJsonButton.Click += (sender, e) =>
            {
                try
                {
                    JsonTestHelper.ShowTestResult();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"JSON测试时发生错误:\n{ex.Message}", "错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            // 添加控件到窗体
            form.Controls.Add(titleLabel);
            form.Controls.Add(descLabel);
            form.Controls.Add(openButton);
            form.Controls.Add(checkApiButton);
            form.Controls.Add(testJsonButton);

            return form;
        }
    }

    /// <summary>
    /// 科室管理页面功能说明
    /// </summary>
    public static class DepartmentManagementFeatures
    {
        /// <summary>
        /// 显示功能说明对话框
        /// </summary>
        public static void ShowFeatureDescription()
        {
            string features = @"科室管理页面主要功能：

1. 科室管理
   - 添加新科室（科室名称、地址、挂号费）
   - 查看所有科室列表
   - 清除输入表单
   - 修改科室信息（预留功能）

2. 病例管理
   - 为指定科室添加病例
   - 查看科室下的所有病例
   - 删除病例信息

3. 数据交互
   - 与后台API服务器进行数据交互
   - 支持异步操作和错误处理
   - 实时更新界面数据

4. 用户界面
   - 左右分栏布局
   - 树形结构显示科室和病例
   - 友好的用户交互体验

API接口说明：
- GET /api/SmartHealthcare/Handle - 获取科室列表
- POST /api/SmartHealthcare/CreateDepartment - 创建科室
- GET /api/SmartHealthcare/GetDepartment - 获取科室下拉列表
- POST /api/SmartHealthcare/CreateMedicalCase - 创建病例";

            MessageBox.Show(features, "科室管理页面功能说明", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
