using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Newtonsoft.Json;

namespace SmartHealthcare
{
    public partial class DepartmentManagementForm : Form
    {
        private List<Department> departments = new List<Department>();
        private List<MedicalCase> medicalCases = new List<MedicalCase>();

        public DepartmentManagementForm()
        {
            InitializeComponent();
        }

        private async void DepartmentManagementForm_Load(object sender, EventArgs e)
        {
            await LoadDepartments();
            await LoadDepartmentComboBox();
        }

        private async Task LoadDepartments()
        {
            try
            {
                string url = APIURL.SReadURL + "api/SmartHealthcare/Handle";
                string response = await HttpClientHelper.ClientAsync("GET", url, true);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<Department>>>(response);
                    if (result.Success)
                    {
                        departments = result.Data;
                        PopulateTreeList();
                    }
                    else
                    {
                        MessageBox.Show($"加载科室失败: {result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"加载科室失败: {response}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载科室时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateTreeList()
        {
            treeViewDepartments.BeginUpdate();
            treeViewDepartments.Nodes.Clear();

            foreach (var department in departments)
            {
                TreeNode departmentNode = new TreeNode(department.Name);
                departmentNode.Tag = department;
                treeViewDepartments.Nodes.Add(departmentNode);
            }

            treeViewDepartments.EndUpdate();
        }

        private async void treeViewDepartments_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node != null && e.Node.Tag is Department department)
            {
                await LoadMedicalCases(department.Id);

                // 清除现有的病例子节点
                e.Node.Nodes.Clear();

                // 添加病例子节点
                foreach (var medicalCase in medicalCases.Where(mc => mc.DepartmentId == department.Id))
                {
                    TreeNode caseNode = new TreeNode(medicalCase.Name);
                    caseNode.Tag = medicalCase;
                    e.Node.Nodes.Add(caseNode);
                }

                e.Node.Expand();
            }
        }

        private async Task LoadMedicalCases(int departmentId)
        {
            try
            {
                string url = APIURL.ReadURL + $"api/SmartHealthcare/GetMedicalCases?departmentId={departmentId}";
                string response = await HttpClientHelper.ClientAsync("GET", url, true);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<MedicalCase>>>(response);
                    if (result.Success)
                    {
                        medicalCases.AddRange(result.Data.Where(mc => !medicalCases.Any(existing => existing.Id == mc.Id)));
                    }
                }
            }
            catch (Exception ex)
            {
                // 静默处理错误，不影响主要功能
            }
        }

        private async Task LoadDepartmentComboBox()
        {
            try
            {
                string url = APIURL.SReadURL + "api/SmartHealthcare/GetDepartment";
                string response = await HttpClientHelper.ClientAsync("GET", url, true);

                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<List<Department>>>(response);
                    if (result.Success)
                    {
                        comboBoxDepartment.Items.Clear();
                        comboBoxDepartment.DisplayMember = "Name";
                        comboBoxDepartment.ValueMember = "Id";

                        foreach (var dept in result.Data)
                        {
                            comboBoxDepartment.Items.Add(dept);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载科室下拉列表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnAddDepartment_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtDepartmentName.Text) ||
                string.IsNullOrWhiteSpace(txtDepartmentAddress.Text) ||
                string.IsNullOrWhiteSpace(txtRegistrationFee.Text))
            {
                MessageBox.Show("请填写完整的科室信息", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!decimal.TryParse(txtRegistrationFee.Text, out decimal registrationFee))
            {
                MessageBox.Show("挂号费必须是有效的数字", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var departmentData = new
                {
                    Name = txtDepartmentName.Text.Trim(),
                    Address = txtDepartmentAddress.Text.Trim(),
                    RegistrationFee = registrationFee
                };

                string json = JsonConvert.SerializeObject(departmentData);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                
                string url = APIURL.SWriteURl + "api/SmartHealthcare/CreateDepartment";
                string response = await HttpClientHelper.ClientAsync("POST", url, true, content);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<object>>(response);
                    if (result.Success)
                    {
                        MessageBox.Show("科室添加成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ClearDepartmentForm();
                        await LoadDepartments();
                        await LoadDepartmentComboBox();
                    }
                    else
                    {
                        MessageBox.Show($"添加科室失败: {result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"添加科室失败: {response}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加科室时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClearDepartment_Click(object sender, EventArgs e)
        {
            ClearDepartmentForm();
        }

        private void ClearDepartmentForm()
        {
            txtDepartmentName.Text = "";
            txtDepartmentAddress.Text = "";
            txtRegistrationFee.Text = "";
        }

        private async void btnAddCase_Click(object sender, EventArgs e)
        {
            if (comboBoxDepartment.SelectedItem == null || string.IsNullOrWhiteSpace(txtCaseName.Text))
            {
                MessageBox.Show("请选择科别并输入病例名称", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var department = (Department)comboBoxDepartment.SelectedItem;
                var caseData = new
                {
                    DepartmentId = department.Id,
                    Name = txtCaseName.Text.Trim()
                };

                string json = JsonConvert.SerializeObject(caseData);
                StringContent content = new StringContent(json, Encoding.UTF8, "application/json");
                
                string url = APIURL.SWriteURl + "api/SmartHealthcare/CreateMedicalCase";
                string response = await HttpClientHelper.ClientAsync("POST", url, true, content);
                
                if (!string.IsNullOrEmpty(response) && !response.StartsWith("HTTP错误"))
                {
                    var result = JsonConvert.DeserializeObject<ApiResponse<object>>(response);
                    if (result.Success)
                    {
                        MessageBox.Show("病例添加成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ClearCaseForm();
                        await LoadDepartments();
                    }
                    else
                    {
                        MessageBox.Show($"添加病例失败: {result.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show($"添加病例失败: {response}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加病例时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeleteCase_Click(object sender, EventArgs e)
        {
            ClearCaseForm();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ClearCaseForm()
        {
            comboBoxDepartment.SelectedItem = null;
            txtCaseName.Text = "";
        }
    }

    // 数据模型类
    public class Department
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public decimal RegistrationFee { get; set; }
    }

    public class MedicalCase
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public int DepartmentId { get; set; }
    }

    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public T Data { get; set; }
    }
}
