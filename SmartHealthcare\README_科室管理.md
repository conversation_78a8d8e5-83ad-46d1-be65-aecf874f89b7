# 科室管理页面使用说明

## 概述
科室管理页面是智慧医疗系统中的一个重要功能模块，用于管理医院的科室信息和病例信息。页面采用左右分栏布局，提供直观的用户界面和完整的数据管理功能。

## 页面截图
根据您提供的需求图片，页面包含：
- 左侧：科室和病例的树形显示区域
- 右侧：科室和病例的添加功能区域

## 主要功能

### 1. 科室管理（右侧上方）
- **科别名称**：输入科室的名称
- **科室地址**：输入科室的物理位置
- **挂号费**：输入该科室的挂号费用
- **添加按钮**：保存科室信息到数据库
- **清除按钮**：清空所有输入字段
- **修改科室按钮**：预留的修改功能

### 2. 病例管理（右侧下方）
- **科别下拉框**：选择要添加病例的科室
- **名称输入框**：输入病例的名称
- **添加按钮**：保存病例信息
- **删除按钮**：清空病例输入字段
- **取消按钮**：关闭科室管理窗口

### 3. 科室树形显示（左侧）
- 显示所有科室的列表
- 点击科室可展开显示该科室下的病例
- 支持树形结构的展开和收缩

## 技术实现

### 使用的技术栈
- **UI框架**：Windows Forms + 部分DevExpress控件
- **数据交互**：HttpClient + JSON序列化
- **异步处理**：async/await模式
- **控件类型**：TreeView、GroupBox、TextBox、ComboBox、Button

### 文件结构
```
SmartHealthcare/
├── DepartmentManagementForm.cs              # 主要业务逻辑
├── DepartmentManagementForm.Designer.cs     # UI设计器文件
├── DepartmentManagementDemo.cs              # 演示程序
├── TestDepartmentForm.cs                    # 测试窗体
├── 科室管理页面说明.md                       # 详细功能说明
└── README_科室管理.md                        # 本文件
```

## API接口

### 1. 获取科室列表
```
GET /api/SmartHealthcare/Handle
用途：获取所有科室信息用于左侧树形显示
```

### 2. 创建科室
```
POST /api/SmartHealthcare/CreateDepartment
参数：{
  "Name": "科室名称",
  "Address": "科室地址", 
  "RegistrationFee": 挂号费数值
}
```

### 3. 获取科室下拉列表
```
GET /api/SmartHealthcare/GetDepartment
用途：为病例添加功能提供科室选择选项
```

### 4. 创建病例
```
POST /api/SmartHealthcare/CreateMedicalCase
参数：{
  "DepartmentId": 科室ID,
  "Name": "病例名称"
}
```

## 使用方法

### 方法1：通过主系统
1. 启动智慧医疗系统主程序
2. 进入"系统管理"页面
3. 点击"科室管理"按钮
4. 科室管理窗口将以对话框形式打开

### 方法2：直接运行演示程序
1. 编译项目
2. 运行 `DepartmentManagementDemo.cs` 中的Main方法
3. 点击"打开科室管理页面"按钮

### 方法3：使用测试窗体
1. 创建 `TestDepartmentForm` 实例
2. 调用 `ShowDialog()` 方法

## 数据验证

### 科室添加验证
- 科室名称不能为空
- 科室地址不能为空
- 挂号费必须是有效的数字

### 病例添加验证
- 必须选择科别
- 病例名称不能为空

## 错误处理
- 所有API调用都包含完整的错误处理
- 网络错误、超时等异常都有友好的用户提示
- 数据验证失败时显示具体的错误信息

## 注意事项

1. **API服务器**：使用前需要确保后台API服务器正常运行
2. **权限认证**：部分API调用需要Bearer Token认证
3. **数据同步**：添加科室或病例后，界面会自动刷新数据
4. **网络连接**：需要稳定的网络连接才能正常使用

## 扩展功能

页面预留了以下扩展功能的接口：
- 科室信息修改
- 病例信息删除
- 批量操作
- 数据导入导出
- 权限管理

## 故障排除

### 常见问题
1. **页面无法打开**：检查项目引用和编译错误
2. **API调用失败**：检查网络连接和API服务器状态
3. **数据不显示**：检查API返回数据格式和解析逻辑
4. **界面显示异常**：检查控件属性设置和布局

### 调试建议
1. 使用演示程序中的"检查API状态"功能
2. 查看控制台输出的错误信息
3. 检查API服务器的日志文件
4. 验证数据库连接和数据表结构

## 联系支持
如果在使用过程中遇到问题，请：
1. 查看详细的错误信息
2. 检查API服务器状态
3. 参考项目文档和代码注释
4. 联系开发团队获取技术支持
