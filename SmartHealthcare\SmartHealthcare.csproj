<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6832C4CF-7F85-4826-8129-5C852DEC0630}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>SmartHealthcare</RootNamespace>
    <AssemblyName>SmartHealthcare</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.Desktop.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v23.1.Core, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v23.1, Version=23.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="APIURL.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="HttpClientHelper.cs" />
    <Compile Include="Login.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Login.Designer.cs">
      <DependentUpon>Login.cs</DependentUpon>
    </Compile>
    <Compile Include="LoginResponse.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RegistrationFormDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RegistrationFormDesigner.Designer.cs">
      <DependentUpon>RegistrationFormDesigner.cs</DependentUpon>
    </Compile>
    <Compile Include="HospitalizationFormDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HospitalizationFormDesigner.Designer.cs">
      <DependentUpon>HospitalizationFormDesigner.cs</DependentUpon>
    </Compile>
    <Compile Include="OutpatientFormDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OutpatientFormDesigner.Designer.cs">
      <DependentUpon>OutpatientFormDesigner.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemManagementForm.Designer.cs">
      <DependentUpon>SystemManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DoorDoctorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DoorDoctorForm.Designer.cs">
      <DependentUpon>DoorDoctorForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DoorManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DoorManagementForm.Designer.cs">
      <DependentUpon>DoorManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DoorChargeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DoorChargeForm.Designer.cs">
      <DependentUpon>DoorChargeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DepartmentManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DepartmentManagementForm.Designer.cs">
      <DependentUpon>DepartmentManagementForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DepartmentManagementDemo.cs" />
    <Compile Include="DepartmentManagementTest.cs" />
    <Compile Include="TestDepartmentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TokenDto.cs" />
    <Compile Include="UserInfo.cs" />
    <EmbeddedResource Include="DepartmentManagementForm.resx">
      <DependentUpon>DepartmentManagementForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Login.resx">
      <DependentUpon>Login.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>